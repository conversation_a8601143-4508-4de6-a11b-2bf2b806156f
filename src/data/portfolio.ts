import { PersonalInfo, Project, Education, Experience, Award, Activity, Skill } from '@/types';

export const personalInfo: PersonalInfo = {
  name: "SOUHAIL OUARGUI",
  title: "Software Engineering Student",
  email: "<EMAIL>",
  phone: "+212 608963887",
  location: "Mohammedia, Morocco",
  bio: "I'm a full-stack(MERN) web and mobile developer with a knack for inventing smart IOT solutions. My expertise in both front-end and back-end development allows me to create seamless digital experiences that work across platforms and devices. Let's connect and explore how I can help take your projects to the next level.",
  profileImage: "/images/profile.jpeg", // You'll place your photo here
  socialLinks: {
    github: "https://github.com/souhailOUARGUI",
    linkedin: "https://www.linkedin.com/in/souhail-ouargui-228b9b212/",
    twitter: "https://twitter.com/SouhailOuargui_",
    website: "https://yourwebsite.com"
  }
};

export const projects: Project[] = [
  {
    id: "1",
    title: "E-Banking Platform",
    description: "A full-stack e-commerce platform built with React, Node.js, and MongoDB. Features include user authentication, product catalog, shopping cart, and payment integration.",
    technologies: ["Spring-boot", "Angular", "MySql", "Swagger-UI","JWT"],
    githubUrl: "https://github.com/yourusername/ecommerce-platform",
    liveUrl: "https://your-ecommerce-demo.com",
    imageUrl: "/images/projects/ecommerce.jpg",
    featured: true
  },
  {
    id: "2",
    title: "Pet SmartHouse",
    description: "Like any PetHouse, Our Pet SmartHouse is designed to provide shelter for dogs, regardless of the weather. However, the new design incorporates several new features such as an automatic dog feeding source, a water bowl with level detection, and an integrated monitoring system inside the kennel, including a camera and a presence detector, all is controller ith a mobile app.",
    technologies: ["Flutter", "Firebase", "IOT"],
    githubUrl: "https://github.com/yourusername/task-manager",
    liveUrl: "https://your-task-manager.com",
    imageUrl: "/images/projects/task-manager.jpg",
    featured: true
  },
  {
    id: "3",
    title: "Factory machines monitoring app",
    description: "This is a mobile application developed to provide real-time supervision and monitoring of SITI factory machines, It aims to enhance productivity, improve operational efficiency, and enable proactive maintenance in the factory environment.",
    technologies: ["React-Native", "Mongo-DB",],
    githubUrl: "https://github.com/yourusername/weather-dashboard",
    liveUrl: "https://your-weather-app.com",
    imageUrl: "/images/projects/weather.jpg",
    featured: false
  }
];

export const education: Education[] = [
  {
    id: "1",
    institution: "ENSET Mohammedia",
    degree: "Engineer's degree",
    field: "Software Engineering",
    startDate: "2024",
    endDate: "2026",
    // gpa: "3.8/4.0",
    // description: "Relevant coursework: Data Structures & Algorithms, Software Design Patterns, Database Systems, Web Development, Mobile App Development, Computer Networks"
  },
   {
    id: "2",
    institution: "Higher School of Technology of Essaouira - Cadi Ayyad University",
    degree: "university diploma in technology",
    field: "Computer science",
    startDate: "2021",
    endDate: "2022",
    // gpa: "3.9/4.0",
    // description: "Graduated with honors. Active in computer science club and mathematics competitions."
  },
  {
    id: "3",
    institution: "Higher School of Technology of Essaouira - Cadi Ayyad University",
    degree: "Bachelor's degree",
    field: "Software Engineering",
    startDate: "2023",
    endDate: "2023",
    // gpa: "3.9/4.0",
    // description: "Graduated with honors. Active in computer science club and mathematics competitions."
  }
];

export const experiences: Experience[] = [
  {
    id: "1",
    company: "Tech Startup Inc.",
    position: "Software Development Intern",
    startDate: "June 2024",
    endDate: "August 2024",
    description: "Developed and maintained web applications using React and Node.js. Collaborated with senior developers on feature implementation and bug fixes. Participated in code reviews and agile development processes.",
    technologies: ["React", "Node.js", "PostgreSQL", "Git", "Docker"],
    type: "clubs"
  },
  // {
  //   id: "2",
  //   company: "University IT Department",
  //   position: "Student Developer",
  //   startDate: "September 2023",
  //   endDate: "Present",
  //   description: "Part-time position developing internal tools and maintaining university websites. Responsible for frontend development and user experience improvements.",
  //   technologies: ["Vue.js", "PHP", "MySQL", "WordPress"],
  //   type: "part-time"
  // },
  {
    id: "2",
    company: "Centre provinciale de meteorologie Essaouira",
    position: "Backend and Mobile Developer",
    startDate: "July 2024",
    endDate: "Present",
    description: "Weather messages management system.",
    technologies: ["React Native", "Node.js", "MongoDB", "Socket.io"],
    type: "internship"
  },
  {
    id: "3",
    company: "Siti Tea",
    position: "Mobile Developer",
    startDate: "April 2023",
    endDate: "Mai 2023",
    description: "Developed a mobile application for the Siti Tea factory to monitor and control the factory machines.",
    technologies: ["React Native.js", "MongoDB"],
    type: "internship"},
  {
    id: "4",
    company: "WebPick",
    position: "Mobile Developer",
    startDate: "April 2022",
    endDate: "Mai 2022",
    description: "Developed a Fully remote controlled DogHouse equiped with automated feeding and monitoring system.",
    technologies: ["Flutter.js", "IOT", "Firebase"],
    type: "internship"
  }
];

export const awards: Award[] = [
  {
    id: "1",
    title: "ENSET Mohamed Best Public Speaker 2024",
    organization: "ENSET Mohammedia",
    date: "April 2024",
    description: "Under the context of GEIW 2024. I won te public speaking competition held at ENSET Mohammedia."
  },
  {
    id: "2",
    title: "Hassan II University Public Speaking Champion",
    organization: "Hassan II University",
    date: "April 2025",
    description: "Under the context of ."
  },
  {
    id: "3",
    title: "Programming Competition Winner",
    organization: "Regional Coding Contest",
    date: "November 2023",
    description: "Placed 1st in regional programming competition, solving complex algorithmic problems."
  }
];

export const activities: Activity[] = [
  {
    id: "1",
    title: "Computer Science Club",
    organization: "Your University",
    role: "Vice President",
    startDate: "September 2023",
    endDate: "Present",
    description: "Organize coding workshops, tech talks, and networking events. Mentor junior students in programming and career development."
  },
  {
    id: "2",
    title: "Open Source Contributor",
    organization: "Various Projects",
    role: "Contributor",
    startDate: "January 2023",
    endDate: "Present",
    description: "Active contributor to open source projects on GitHub. Focus on web development tools and educational resources."
  },
  {
    id: "3",
    title: "Volunteer Coding Instructor",
    organization: "Local Community Center",
    role: "Instructor",
    startDate: "June 2023",
    endDate: "Present",
    description: "Teach basic programming concepts to high school students during summer programs."
  }
];

export const skills: Skill[] = [
  // Frontend
  { name: "React", category: "frontend", proficiency: "intermediate" },
  { name: "Angular.js", category: "frontend", proficiency: "beginner" },
  { name: "TypeScript", category: "frontend", proficiency: "intermediate" },
  { name: "JavaScript", category: "frontend", proficiency: "advanced" },
  { name: "HTML/CSS", category: "frontend", proficiency: "advanced" },
  { name: "Tailwind CSS", category: "frontend", proficiency: "intermediate" },
  // { name: "Vue.js", category: "frontend", proficiency: "intermediate" },
  
  // Backend
  { name: "Node.js", category: "backend", proficiency: "advanced" },
  { name: "Express.js", category: "backend", proficiency: "intermediate" },
  { name: "Java", category: "backend", proficiency: "advanced" },
  { name: "Spring-boot", category: "backend", proficiency: "intermediate" },
  
  // Database
  { name: "MongoDB", category: "database", proficiency: "intermediate" },
  { name: "Oracle", category: "database", proficiency: "advanced" },
  // { name: "PostgreSQL", category: "database", proficiency: "intermediate" },
  { name: "MySQL", category: "database", proficiency: "intermediate" },
  { name: "Firebase", category: "database", proficiency: "intermediate" },
  
  // Tools
  { name: "Git", category: "tools", proficiency: "advanced" },
  { name: "Docker", category: "tools", proficiency: "beginner" },
  { name: "VS Code", category: "tools", proficiency: "advanced" },
  // { name: "Figma", category: "tools", proficiency: "intermediate" },
  
  // Languages
  { name: "Arabic", category: "languages", proficiency: "advanced" },
  { name: "English", category: "languages", proficiency: "advanced" },
  { name: "French", category: "languages", proficiency: "advanced" },
  { name: "Spanish", category: "languages", proficiency: "beginner" }
];
